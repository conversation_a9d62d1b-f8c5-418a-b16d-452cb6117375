<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Model Information - Fraud Detection System</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-12 text-center mb-4">
                <h1>Model Information</h1>
                <p class="lead">Details about the fraud detection model</p>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h3>Model Details</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h4>Model Type</h4>
                                <p>{{ model_info.model_type }}</p>
                                
                                <h4>Best Parameters</h4>
                                <ul class="list-group">
                                    {% for param, value in model_info.best_parameters.items() %}
                                    <li class="list-group-item">
                                        <strong>{{ param.replace('classifier__', '') }}:</strong> {{ value }}
                                    </li>
                                    {% endfor %}
                                </ul>
                            </div>
                            
                            <div class="col-md-6">
                                <h4>Performance Metrics</h4>
                                <table class="table table-striped">
                                    <tbody>
                                        <tr>
                                            <th>Accuracy</th>
                                            <td>{{ "%.4f"|format(model_info.metrics.accuracy) }}</td>
                                        </tr>
                                        <tr>
                                            <th>Precision</th>
                                            <td>{{ "%.4f"|format(model_info.metrics.precision) }}</td>
                                        </tr>
                                        <tr>
                                            <th>Recall</th>
                                            <td>{{ "%.4f"|format(model_info.metrics.recall) }}</td>
                                        </tr>
                                        <tr>
                                            <th>F1 Score</th>
                                            <td>{{ "%.4f"|format(model_info.metrics.f1) }}</td>
                                        </tr>
                                    </tbody>
                                </table>
                                
                                <h4>Confusion Matrix</h4>
                                <table class="table table-bordered text-center">
                                    <thead>
                                        <tr>
                                            <th></th>
                                            <th>Predicted Negative</th>
                                            <th>Predicted Positive</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <th>Actual Negative</th>
                                            <td class="bg-success text-white">{{ model_info.metrics.confusion_matrix[0][0] }}</td>
                                            <td class="bg-warning">{{ model_info.metrics.confusion_matrix[0][1] }}</td>
                                        </tr>
                                        <tr>
                                            <th>Actual Positive</th>
                                            <td class="bg-danger">{{ model_info.metrics.confusion_matrix[1][0] }}</td>
                                            <td class="bg-success text-white">{{ model_info.metrics.confusion_matrix[1][1] }}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <h4>Feature Importance</h4>
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Feature</th>
                                            <th>Importance</th>
                                            <th>Visualization</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for feature in model_info.feature_importance|sort(attribute='Importance', reverse=True) %}
                                        <tr>
                                            <td>{{ feature.Feature }}</td>
                                            <td>{{ "%.4f"|format(feature.Importance) }}</td>
                                            <td>
                                                <div class="progress">
                                                    <div class="progress-bar bg-primary" role="progressbar" 
                                                         style="width: {{ feature.Importance * 100 }}%"
                                                         aria-valuenow="{{ feature.Importance * 100 }}" 
                                                         aria-valuemin="0" aria-valuemax="100">
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-md-12 text-center">
                <a href="/" class="btn btn-primary">Return to Home</a>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
