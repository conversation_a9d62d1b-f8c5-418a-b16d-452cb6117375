<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fraud Detection Result</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-12 text-center mb-4">
                <h1>Fraud Detection Result</h1>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h3>Prediction Result</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12 text-center mb-4">
                                {% if result.is_fraud %}
                                <div class="alert alert-danger">
                                    <h2>Potential Fraud Detected!</h2>
                                    <p>Fraud Probability: {{ "%.2f"|format(result.fraud_probability * 100) }}%</p>
                                    <p>Risk Level: <span class="badge bg-danger">{{ result.risk_level|upper }}</span></p>
                                </div>
                                {% else %}
                                <div class="alert alert-success">
                                    <h2>Transaction Appears Legitimate</h2>
                                    <p>Fraud Probability: {{ "%.2f"|format(result.fraud_probability * 100) }}%</p>
                                    <p>Risk Level: <span class="badge bg-{{ 'warning' if result.risk_level == 'medium' else 'success' }}">{{ result.risk_level|upper }}</span></p>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <h4>Transaction Details</h4>
                                <table class="table table-striped">
                                    <tbody>
                                        <tr>
                                            <th>Transaction Date/Time</th>
                                            <td>{{ transaction.trans_date_trans_time }}</td>
                                        </tr>
                                        <tr>
                                            <th>Credit Card Number</th>
                                            <td>{{ transaction.cc_num }}</td>
                                        </tr>
                                        <tr>
                                            <th>Merchant</th>
                                            <td>{{ transaction.merchant }}</td>
                                        </tr>
                                        <tr>
                                            <th>Category</th>
                                            <td>{{ transaction.category }}</td>
                                        </tr>
                                        <tr>
                                            <th>Amount</th>
                                            <td>${{ "%.2f"|format(transaction.amt) }}</td>
                                        </tr>
                                        <tr>
                                            <th>Cardholder</th>
                                            <td>{{ transaction.first }} {{ transaction.last }}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-md-12 text-center">
                <a href="/" class="btn btn-primary">Check Another Transaction</a>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
