# 🚀 Fraud Detection System - Deployment Guide

## Quick Start Options

### Option 1: Local Development (Fastest)

```bash
# Terminal 1 - Start API
cd /Users/<USER>/task_fraud_detection
python -m src.api.app

# Terminal 2 - Start Web UI  
cd /Users/<USER>/task_fraud_detection
python -m src.web.app
```

**Access Points:**
- 🌐 Web Interface: http://localhost:8501
- 🔗 API: http://localhost:8001
- 📚 API Documentation: http://localhost:8001/docs

---

### Option 2: Docker Deployment (Recommended)

```bash
# Build and run with Docker Compose
cd /Users/<USER>/task_fraud_detection
docker-compose -f deployment/docker-compose.yml up --build

# Or run in background
docker-compose -f deployment/docker-compose.yml up -d --build
```

**Access Points:**
- 🌐 Web Interface: http://localhost:8501
- 🔗 API: http://localhost:8000
- 📚 API Documentation: http://localhost:8000/docs

---

### Option 3: Cloud Deployment (Production)

#### Google Cloud Run

```bash
# Make the script executable
chmod +x deployment/cloud_run.sh

# Deploy to Google Cloud
./deployment/cloud_run.sh
```

#### Manual Cloud Deployment

```bash
# 1. Build Docker image
docker build -t fraud-detection .

# 2. Tag for your registry (replace with your details)
docker tag fraud-detection gcr.io/YOUR_PROJECT_ID/fraud-detection

# 3. Push to registry
docker push gcr.io/YOUR_PROJECT_ID/fraud-detection

# 4. Deploy to cloud service of choice
```

---

## 🔧 Configuration for Production

### Environment Variables

Create a `.env` file for production:

```bash
# API Configuration
API_HOST=0.0.0.0
API_PORT=8001

# Web UI Configuration  
WEB_HOST=0.0.0.0
WEB_PORT=8501

# Security (add for production)
SECRET_KEY=your-secret-key-here
ALLOWED_HOSTS=your-domain.com,localhost

# Database (if needed)
DATABASE_URL=your-database-url
```

### Production Checklist

- [ ] Set proper environment variables
- [ ] Configure HTTPS/SSL certificates
- [ ] Set up monitoring and logging
- [ ] Configure backup for models and data
- [ ] Set up CI/CD pipeline
- [ ] Configure load balancing (if needed)
- [ ] Set up health checks
- [ ] Configure proper security headers

---

## 🧪 Testing Your Deployment

### API Testing

```bash
# Health check
curl http://localhost:8001/health

# Model info
curl http://localhost:8001/model-info

# Test prediction
curl -X POST "http://localhost:8001/predict" \
  -H "Content-Type: application/json" \
  -d '{
    "cc_num": "****************",
    "merchant": "fraud_Rippin, Kub and Mann",
    "category": "misc_net",
    "amt": 2.95,
    "first": "Jennifer",
    "last": "Banks",
    "gender": "F",
    "street": "561 Perry Cove",
    "city": "Moravian Falls",
    "state": "NC",
    "zip": 28654,
    "lat": 36.0788,
    "long": -81.1781,
    "city_pop": 3495,
    "job": "Psychologist, counselling",
    "dob": "1988-03-09",
    "trans_num": "0b242abb623afc578575b8445e35d90c",
    "unix_time": **********,
    "merch_lat": 36.011293,
    "merch_long": -82.048315
  }'
```

### Web UI Testing

1. Open http://localhost:8501
2. Fill in transaction details
3. Click "Predict Fraud"
4. Verify results display correctly

---

## 🔍 Monitoring & Maintenance

### Logs

```bash
# Docker logs
docker-compose logs -f

# API logs
docker-compose logs api

# Web UI logs  
docker-compose logs web
```

### Health Monitoring

- API Health: http://localhost:8001/health
- Model Status: http://localhost:8001/model-info
- Web UI Status: Check if http://localhost:8501 loads

### Performance Monitoring

- Monitor API response times
- Track prediction accuracy over time
- Monitor resource usage (CPU, memory)
- Set up alerts for failures

---

## 🚨 Troubleshooting

### Common Issues

1. **Port conflicts**: Change ports in docker-compose.yml
2. **Model not found**: Ensure models/ directory is mounted
3. **Import errors**: Check PYTHONPATH environment variable
4. **Permission issues**: Check file permissions for data/ and models/

### Debug Commands

```bash
# Check container status
docker-compose ps

# Access container shell
docker-compose exec fraud-detection bash

# View container logs
docker-compose logs --tail=50 fraud-detection
```

---

## 🎯 Next Steps After Going Live

1. **Monitor Performance**: Set up logging and monitoring
2. **Model Updates**: Plan for periodic model retraining
3. **Security**: Implement authentication and rate limiting
4. **Scaling**: Consider load balancing for high traffic
5. **Backup**: Set up automated backups for models and data
6. **Documentation**: Create user guides and API documentation

Your fraud detection system is ready for production! 🚀
